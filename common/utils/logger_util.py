# -*- coding: utf-8 -*-
# @Time   : 2025/08/07 
# <AUTHOR> AI Assistant
# @File   : logger_util.py
# @Project: ai-service
# @Desc   : 统一的日志配置模块，支持文件日志和控制台日志

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional


class LoggerConfig:
    """日志配置类"""
    
    def __init__(self, 
                 name: str = "ai-service",
                 log_level: str = "INFO",
                 log_dir: str = "logs",
                 max_file_size: int = 10 * 1024 * 1024,  # 10MB
                 backup_count: int = 5,
                 console_output: bool = True,
                 file_output: bool = True):
        """
        初始化日志配置
        
        Args:
            name: 日志器名称
            log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            log_dir: 日志文件目录
            max_file_size: 单个日志文件最大大小（字节）
            backup_count: 日志文件备份数量
            console_output: 是否输出到控制台
            file_output: 是否输出到文件
        """
        self.name = name
        self.log_level = getattr(logging, log_level.upper())
        self.log_dir = log_dir
        self.max_file_size = max_file_size
        self.backup_count = backup_count
        self.console_output = console_output
        self.file_output = file_output
        
        # 创建日志目录
        Path(self.log_dir).mkdir(parents=True, exist_ok=True)
        
        # 初始化日志器
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志器"""
        logger = logging.getLogger(self.name)
        logger.setLevel(self.log_level)
        
        # 避免重复添加处理器
        if logger.handlers:
            return logger
        
        # 创建格式化器
        formatter = logging.Formatter(
            fmt='%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 控制台处理器
        if self.console_output:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(self.log_level)
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
        
        # 文件处理器
        if self.file_output:
            # 应用日志文件
            app_log_file = os.path.join(self.log_dir, f"{self.name}.log")
            file_handler = logging.handlers.RotatingFileHandler(
                app_log_file,
                maxBytes=self.max_file_size,
                backupCount=self.backup_count,
                encoding='utf-8'
            )
            file_handler.setLevel(self.log_level)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
            
            # 错误日志文件（只记录ERROR及以上级别）
            error_log_file = os.path.join(self.log_dir, f"{self.name}_error.log")
            error_handler = logging.handlers.RotatingFileHandler(
                error_log_file,
                maxBytes=self.max_file_size,
                backupCount=self.backup_count,
                encoding='utf-8'
            )
            error_handler.setLevel(logging.ERROR)
            error_handler.setFormatter(formatter)
            logger.addHandler(error_handler)
        
        return logger
    
    def get_logger(self) -> logging.Logger:
        """获取日志器实例"""
        return self.logger


# 全局日志器实例
_global_logger_config = None
_global_logger = None


def init_logger(name: str = "ai-service",
                log_level: str = "INFO",
                log_dir: str = "logs",
                max_file_size: int = 10 * 1024 * 1024,
                backup_count: int = 5,
                console_output: bool = True,
                file_output: bool = True) -> logging.Logger:
    """
    初始化全局日志器
    
    Args:
        name: 日志器名称
        log_level: 日志级别
        log_dir: 日志目录
        max_file_size: 单个日志文件最大大小
        backup_count: 日志文件备份数量
        console_output: 是否输出到控制台
        file_output: 是否输出到文件
    
    Returns:
        logging.Logger: 日志器实例
    """
    global _global_logger_config, _global_logger
    
    _global_logger_config = LoggerConfig(
        name=name,
        log_level=log_level,
        log_dir=log_dir,
        max_file_size=max_file_size,
        backup_count=backup_count,
        console_output=console_output,
        file_output=file_output
    )
    _global_logger = _global_logger_config.get_logger()
    return _global_logger


def get_logger(name: Optional[str] = None) -> logging.Logger:
    """
    获取日志器实例
    
    Args:
        name: 日志器名称，如果为None则返回全局日志器
    
    Returns:
        logging.Logger: 日志器实例
    """
    global _global_logger
    
    if name is None:
        if _global_logger is None:
            # 如果全局日志器未初始化，使用默认配置初始化
            return init_logger()
        return _global_logger
    else:
        # 创建指定名称的日志器
        logger_config = LoggerConfig(name=name)
        return logger_config.get_logger()


def log_function_call(func_name: str, args: tuple = (), kwargs: dict = None, logger: Optional[logging.Logger] = None):
    """
    记录函数调用日志的装饰器辅助函数
    
    Args:
        func_name: 函数名称
        args: 位置参数
        kwargs: 关键字参数
        logger: 日志器实例
    """
    if logger is None:
        logger = get_logger()
    
    kwargs = kwargs or {}
    
    # 过滤敏感信息
    safe_args = []
    for arg in args:
        if isinstance(arg, str) and len(arg) > 100:
            safe_args.append(f"{arg[:100]}...")
        else:
            safe_args.append(arg)
    
    safe_kwargs = {}
    for key, value in kwargs.items():
        if key.lower() in ['password', 'token', 'key', 'secret']:
            safe_kwargs[key] = "***"
        elif isinstance(value, str) and len(value) > 100:
            safe_kwargs[key] = f"{value[:100]}..."
        else:
            safe_kwargs[key] = value
    
    logger.info(f"调用函数 {func_name}, 参数: args={safe_args}, kwargs={safe_kwargs}")


def log_execution_time(func_name: str, start_time: datetime, end_time: datetime, logger: Optional[logging.Logger] = None):
    """
    记录函数执行时间
    
    Args:
        func_name: 函数名称
        start_time: 开始时间
        end_time: 结束时间
        logger: 日志器实例
    """
    if logger is None:
        logger = get_logger()
    
    execution_time = (end_time - start_time).total_seconds()
    logger.info(f"函数 {func_name} 执行完成，耗时: {execution_time:.3f}秒")


def log_exception(exception: Exception, context: str = "", logger: Optional[logging.Logger] = None):
    """
    记录异常日志
    
    Args:
        exception: 异常对象
        context: 异常上下文信息
        logger: 日志器实例
    """
    if logger is None:
        logger = get_logger()
    
    import traceback
    
    error_msg = f"异常发生: {type(exception).__name__}: {str(exception)}"
    if context:
        error_msg = f"{context} - {error_msg}"
    
    logger.error(error_msg)
    logger.error(f"异常堆栈:\n{traceback.format_exc()}")


# 便捷的日志记录函数
def debug(msg: str, logger: Optional[logging.Logger] = None):
    """记录DEBUG级别日志"""
    if logger is None:
        logger = get_logger()
    logger.debug(msg)


def info(msg: str, logger: Optional[logging.Logger] = None):
    """记录INFO级别日志"""
    if logger is None:
        logger = get_logger()
    logger.info(msg)


def warning(msg: str, logger: Optional[logging.Logger] = None):
    """记录WARNING级别日志"""
    if logger is None:
        logger = get_logger()
    logger.warning(msg)


def error(msg: str, logger: Optional[logging.Logger] = None):
    """记录ERROR级别日志"""
    if logger is None:
        logger = get_logger()
    logger.error(msg)


def critical(msg: str, logger: Optional[logging.Logger] = None):
    """记录CRITICAL级别日志"""
    if logger is None:
        logger = get_logger()
    logger.critical(msg)


if __name__ == "__main__":
    # 测试日志功能
    logger = init_logger("test-logger", "DEBUG")
    
    logger.debug("这是一条DEBUG日志")
    logger.info("这是一条INFO日志")
    logger.warning("这是一条WARNING日志")
    logger.error("这是一条ERROR日志")
    logger.critical("这是一条CRITICAL日志")
    
    # 测试异常日志
    try:
        1 / 0
    except Exception as e:
        log_exception(e, "测试异常记录")
    
    print("日志测试完成，请查看logs目录下的日志文件")
