# -*- coding: utf-8 -*-
# @Time   : 2025/03/18 14:30
# <AUTHOR> 和森
# @Email  : <EMAIL>
# @File   : ai_cr.py
# @Project: app-service
# @Desc   : 主要实现了使用大模型进行CR建议的功能

import sys
sys.path.append('/Users/<USER>/ai-service')  # 添加这一行在导入common模块之前

from datetime import datetime
from common.config.config import (LLM_MODEL, LLM_API_KEY, LLM_API_URL, REVIEW_MAX_TOKENS, REVIEW_MAX_CODE_PLUS)
from common.basic.prompts import CODE_REVIEW_PROMPT, GIT_DIFF_INFO_PROMPT
from common.thirdparty.gitlabapi import GitLabAPI
from common.utils.token_util import count_tokens, truncate_text_by_tokens
from common.utils.git_diff_format import annotate_added_line_numbers
from common.utils.logger_util import get_logger, log_exception, log_function_call, log_execution_time
from common.basic.exception import GitlabApiException, MRFileChangeException, MRFetchException
import sys
import requests
import json
import fnmatch
from llama_index.llms.openai import OpenAI
from llama_index.core.llms import ChatMessage
from llama_index.core.prompts import PromptTemplate

# 获取日志器
logger = get_logger("code_review")


# 定义指定文件类型
SPECIFIC_FILE_TYPES = [
    "*.go",  # 只处理 Go 语言文件
    "*.java",  # 只处理 Java 语言文件
    "*.py",  # 只处理 Python 语言文件
    "*.js",  # 只处理 JavaScript 语言文件
    "*.ts",  # 只处理 TypeScript 语言文件
]

def filter_files(file_list):
    """过滤掉匹配 IGNORE_PATTERNS 规则的文件"""
    logger.info(f"开始过滤文件列表，总文件数: {len(file_list)}")
    logger.debug(f"输入文件列表: {file_list}")

    filtered_files = []
    for file in file_list:
        if not any(fnmatch.fnmatch(file, pattern) for pattern in SPECIFIC_FILE_TYPES):
            logger.debug(f"文件 {file} 不匹配指定类型，跳过")
            continue
        filtered_files.append(file)
        logger.debug(f"文件 {file} 匹配指定类型，保留")

    logger.info(f"文件过滤完成，保留文件数: {len(filtered_files)}")
    logger.debug(f"过滤后文件列表: {filtered_files}")
    return filtered_files

def filter_mr_changes(project_id, iid):
    """
    获取 MR 变更信息，并只处理 SPECIFIC_FILE_TYPES 中的文件类型

    :param project_id: GitLab 项目的 ID
    :param iid: Merge Request (MR) 的内部 ID
    :return: 返回字典包含:
             - data: 过滤后的变更文件列表(可能为空)
             - status: 执行状态(True/False)
             - reason: 当status为False时的原因说明
    """
    start_time = datetime.now()
    log_function_call("filter_mr_changes", args=(project_id, iid))
    logger.info(f"开始获取MR变更信息 - 项目ID: {project_id}, MR ID: {iid}")

    try:
        logger.info(f"调用GitLab API获取MR变更 - 项目ID: {project_id}, MR ID: {iid}")
        res = GitLabAPI().get_mr_changes(project_id, iid)
        changes = res.get("changes", [])

        logger.info(f"获取到原始变更文件数: {len(changes)}")
        logger.debug(f"原始变更数据: {changes}")

        if not changes:
            logger.warning(f"MR中没有文件变更 - 项目ID: {project_id}, MR ID: {iid}")
            raise MRFileChangeException("MR 中没有文件变更")

        filtered_changes = []
        total_diff_plus = 0
        logger.info(f"开始处理变更文件，总数: {len(changes)}")

        for i, change in enumerate(changes, 1):
            new_path = change.get("new_path", "")
            diff = change.get("diff", "")

            logger.debug(f"处理第{i}个文件: {new_path}")
            logger.debug(f"原始diff内容长度: {len(diff)}")

            # 注释行号
            diff = annotate_added_line_numbers(diff)
            logger.debug(f"注释后diff内容长度: {len(diff)}")

            # 检查是否为指定文件类型
            if not any(fnmatch.fnmatch(new_path, pattern) for pattern in SPECIFIC_FILE_TYPES):
                logger.debug(f"文件 {new_path} 不在指定类型范围内，跳过")
                continue

            # 计算新增行数
            diff_plus = diff.count("\n+")
            total_diff_plus += diff_plus
            filtered_changes.append({"new_path": new_path, "diff": diff})

            logger.info(f"文件 {new_path} 符合条件，新增行数: {diff_plus}")

        logger.info(f"文件过滤完成，符合条件的文件数: {len(filtered_changes)}, 总新增行数: {total_diff_plus}")

        if not filtered_changes:
            logger.warning("MR中没有需要处理的代码变更(所有变更文件都不在指定类型范围内)")
            raise MRFileChangeException("MR 中没有需要处理的代码变更(所有变更文件都不在指定类型范围内)")
        elif total_diff_plus > REVIEW_MAX_CODE_PLUS:
            # 新增变更行数超过限制，不进行AI代码评审
            logger.warning(f"新增变更行数({total_diff_plus})超过限制({REVIEW_MAX_CODE_PLUS})，不进行AI代码评审")
            raise MRFileChangeException(f"MR 中需要处理的代码新增变更行数超过{REVIEW_MAX_CODE_PLUS}，不进行AI代码评审.")

        end_time = datetime.now()
        log_execution_time("filter_mr_changes", start_time, end_time)
        logger.info(f"MR变更过滤成功 - 项目ID: {project_id}, MR ID: {iid}, 有效文件数: {len(filtered_changes)}")

        return filtered_changes

    except MRFileChangeException as e:
        end_time = datetime.now()
        log_execution_time("filter_mr_changes", start_time, end_time)
        logger.warning(f"MR变更过滤业务异常 - 项目ID: {project_id}, MR ID: {iid}, 错误: {e.message}")
        raise
    except Exception as e:
        end_time = datetime.now()
        log_execution_time("filter_mr_changes", start_time, end_time)
        logger.error(f"MR变更过滤系统异常 - 项目ID: {project_id}, MR ID: {iid}")
        log_exception(e, f"获取MR变更时发生错误 - 项目ID: {project_id}, MR ID: {iid}")
        raise MRFetchException(f"获取 MR 变更时发生错误: {str(e)}")


def review_mr_changes(project_id, iid):
    """
    评审 MR 代码变更，调用大模型生成评审意见

    :param project_id: GitLab 项目的 ID
    :param iid: Merge Request 的 ID
    :return: 大模型生成的代码评审意见，如果没有需要评审的内容则m返回原因
    """
    start_time = datetime.now()
    log_function_call("review_mr_changes", args=(project_id, iid))
    logger.info(f"开始MR代码审查 - 项目ID: {project_id}, MR ID: {iid}")

    try:
        # 获取并过滤MR变更
        logger.info(f"获取MR变更内容 - 项目ID: {project_id}, MR ID: {iid}")
        result = filter_mr_changes(project_id, iid)
        logger.info(f"成功获取MR变更，文件数: {len(result)}")

        # 定义 PromptTemplate，强调 git diff 解析
        logger.info("创建代码审查prompt模板")
        prompt_template = PromptTemplate(CODE_REVIEW_PROMPT)

        # 格式化 changes 内容
        logger.info("格式化变更内容为prompt格式")
        changes_str = "\n\n".join(
            [f"### 文件: {change['new_path']}\n```diff\n{change['diff']}\n```" for change in result]
        )
        logger.debug(f"格式化后的变更内容长度: {len(changes_str)}")

        # 计算tokens数量，如果超过REVIEW_MAX_TOKENS，截断changes_text
        logger.info("计算变更内容的token数量")
        total_tokens = count_tokens(changes_str)
        logger.info(f"变更内容token数量: {total_tokens}, 最大限制: {REVIEW_MAX_TOKENS}")

        if total_tokens > REVIEW_MAX_TOKENS:
            logger.warning(f"变更内容超过最大token限制({REVIEW_MAX_TOKENS})，将进行截断")
            changes_str = truncate_text_by_tokens(changes_str, REVIEW_MAX_TOKENS)
            logger.info(f"截断后的内容长度: {len(changes_str)}")
        else:
            logger.info("变更内容在token限制范围内，无需截断")


        # 使用 PromptTemplate 生成最终的 prompt
        logger.info("生成最终的审查prompt")
        review_prompt = prompt_template.format(changes=changes_str)
        logger.debug(f"最终prompt长度: {len(review_prompt)}")

        # 调用 LlamaIndex 中的大模型
        logger.info(f"开始调用大模型进行代码审查 - 模型: {LLM_MODEL}")
        llm_start_time = datetime.now()

        llm = OpenAI(
            api_key=LLM_API_KEY,
            api_base=LLM_API_URL,
            default_headers={"model": LLM_MODEL}
        )
        logger.debug(f"大模型客户端初始化完成 - API地址: {LLM_API_URL}")

        messages = [ChatMessage(role="user", content=review_prompt)]
        logger.info("发送请求到大模型...")

        response = llm.chat(messages=messages)

        llm_end_time = datetime.now()
        llm_duration = (llm_end_time - llm_start_time).total_seconds()
        logger.info(f"大模型调用成功，耗时: {llm_duration:.3f}秒")
        logger.debug(f"大模型响应长度: {len(str(response))}")

        end_time = datetime.now()
        log_execution_time("review_mr_changes", start_time, end_time)
        logger.info(f"MR代码审查完成 - 项目ID: {project_id}, MR ID: {iid}")

        return str(response)

    except Exception as e:
        end_time = datetime.now()
        log_execution_time("review_mr_changes", start_time, end_time)
        logger.error(f"MR代码审查异常 - 项目ID: {project_id}, MR ID: {iid}")
        log_exception(e, f"代码审查过程中发生异常 - 项目ID: {project_id}, MR ID: {iid}")
        raise


if __name__ == "__main__":
        changed_files = [
        "src/main.py",
        "docs/readme.md",
        "config/settings.yaml",
        "data/schema.proto",
        "vendor/lib/dependency.txt",
        "src/module.py",
        "src/module.go",
        "src/module.java",
        "src/module.js",
        "src/module.ts",
        "src/module.pyc",
    ]
        # filtered_files = filter_files(changed_files)
        # print("过滤后的文件列表:") 
        # print(filtered_files)
        # res = review_mr_changes(4142, 306)
        # res = review_mr_changes(2609, 995)
        res = review_mr_changes(6900,2693)
        # print(f"res:{res}")

        # filter_mr_changes(4142, 306) 

